// X User Info Enhancer - 加密保护版本
// 此文件需要密钥验证才能正常运行

// 加密保护系统
class EncryptionProtection {
    constructor() {
        this.isUnlocked = false;
        this.decryptedCode = null;
    }

    // 解密函数 - 对于简单的base64编码，直接解码
    decrypt(encryptedText, key) {
        try {
            // 对于这个演示版本，我们使用简单的base64解码
            // 在实际应用中，这里应该使用真正的加密算法
            return atob(encryptedText);
        } catch (error) {
            throw new Error('解密失败');
        }
    }

    // 验证密钥文件
    validateKeyFile(keyFileContent) {
        try {
            const keyFile = JSON.parse(keyFileContent);

            if (!keyFile.version || !keyFile.algorithm || !keyFile.key || !keyFile.checksum) {
                throw new Error('密钥文件格式无效');
            }

            // 计算校验和验证
            const calculatedChecksum = this.calculateChecksum(keyFile.key);
            if (calculatedChecksum !== keyFile.checksum) {
                throw new Error('密钥文件已损坏或被篡改');
            }

            return keyFile;
        } catch (error) {
            throw new Error(`密钥文件验证失败: ${error.message}`);
        }
    }

    // 计算校验和
    calculateChecksum(data) {
        let hash = 0;
        for (let i = 0; i < data.length; i++) {
            const char = data.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return hash.toString(16);
    }

    // 检查是否已解锁
    async checkUnlockStatus() {
        return new Promise((resolve) => {
            chrome.storage.local.get(['keyFileContent', 'isUnlocked'], (data) => {
                if (data.keyFileContent && data.isUnlocked) {
                    try {
                        this.unlockWithKey(data.keyFileContent);
                        resolve(true);
                    } catch (error) {
                        console.error('密钥验证失败:', error);
                        resolve(false);
                    }
                } else {
                    resolve(false);
                }
            });
        });
    }

    // 使用密钥解锁
    async unlockWithKey(keyFileContent) {
        const keyFile = this.validateKeyFile(keyFileContent);

        // 获取加密的代码
        const encryptedData = await this.getEncryptedCode();

        // 解密代码
        this.decryptedCode = this.decrypt(encryptedData, keyFile.key);
        this.isUnlocked = true;

        // 保存解锁状态
        chrome.storage.local.set({ isUnlocked: true });

        return true;
    }

    // 获取加密的代码
    async getEncryptedCode() {
        return new Promise((resolve, reject) => {
            fetch(chrome.runtime.getURL('content.encrypted.json'))
                .then(response => response.json())
                .then(data => {
                    resolve(data.encryptedData);
                })
                .catch(error => {
                    reject(new Error('无法加载加密文件'));
                });
        });
    }

    // 执行解密后的代码
    executeDecryptedCode() {
        if (!this.isUnlocked || !this.decryptedCode) {
            throw new Error('代码未解锁');
        }

        // 在安全的上下文中执行代码
        const script = new Function(this.decryptedCode);
        script();
    }
}

// 初始化加密保护系统
const protection = new EncryptionProtection();

// 主初始化函数
const init = async () => {
    console.log('X User Info Enhancer: 正在验证授权...');

    const isUnlocked = await protection.checkUnlockStatus();

    if (isUnlocked) {
        console.log('X User Info Enhancer: 授权验证成功，正在启动...');
        try {
            protection.executeDecryptedCode();
        } catch (error) {
            console.error('X User Info Enhancer: 启动失败:', error);
        }
    } else {
        console.log('X User Info Enhancer: 需要密钥文件授权，请在插件设置中加载密钥文件');
    }
};

// 启动插件
init();
