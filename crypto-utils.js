// 加密工具模块
class CryptoUtils {
    constructor() {
        this.algorithm = 'AES-GCM';
        this.keyLength = 256;
    }

    // 从密码生成密钥
    async deriveKey(password, salt) {
        const encoder = new TextEncoder();
        const keyMaterial = await crypto.subtle.importKey(
            'raw',
            encoder.encode(password),
            { name: 'PBKDF2' },
            false,
            ['deriveKey']
        );

        return crypto.subtle.deriveKey(
            {
                name: 'PBKDF2',
                salt: salt,
                iterations: 100000,
                hash: 'SHA-256'
            },
            keyMaterial,
            { name: this.algorithm, length: this.keyLength },
            false,
            ['encrypt', 'decrypt']
        );
    }

    // 加密数据
    async encrypt(data, password) {
        const encoder = new TextEncoder();
        const salt = crypto.getRandomValues(new Uint8Array(16));
        const iv = crypto.getRandomValues(new Uint8Array(12));
        
        const key = await this.deriveKey(password, salt);
        const encodedData = encoder.encode(data);
        
        const encrypted = await crypto.subtle.encrypt(
            { name: this.algorithm, iv: iv },
            key,
            encodedData
        );

        // 组合 salt + iv + encrypted data
        const result = new Uint8Array(salt.length + iv.length + encrypted.byteLength);
        result.set(salt, 0);
        result.set(iv, salt.length);
        result.set(new Uint8Array(encrypted), salt.length + iv.length);
        
        return btoa(String.fromCharCode(...result));
    }

    // 解密数据
    async decrypt(encryptedData, password) {
        try {
            const data = new Uint8Array(atob(encryptedData).split('').map(c => c.charCodeAt(0)));
            
            const salt = data.slice(0, 16);
            const iv = data.slice(16, 28);
            const encrypted = data.slice(28);
            
            const key = await this.deriveKey(password, salt);
            
            const decrypted = await crypto.subtle.decrypt(
                { name: this.algorithm, iv: iv },
                key,
                encrypted
            );
            
            const decoder = new TextDecoder();
            return decoder.decode(decrypted);
        } catch (error) {
            throw new Error('解密失败：密码错误或数据损坏');
        }
    }

    // 生成设备指纹
    async generateDeviceFingerprint() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Device fingerprint', 2, 2);
        
        const fingerprint = [
            navigator.userAgent,
            navigator.language,
            screen.width + 'x' + screen.height,
            new Date().getTimezoneOffset(),
            canvas.toDataURL()
        ].join('|');
        
        const encoder = new TextEncoder();
        const data = encoder.encode(fingerprint);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    }
}

// 授权验证系统
class AuthSystem {
    constructor() {
        this.cryptoUtils = new CryptoUtils();
        this.serverUrl = 'https://your-auth-server.com/api'; // 替换为您的服务器地址
        this.maxRetries = 3;
        this.lockoutTime = 30 * 60 * 1000; // 30分钟锁定时间
    }

    // 验证密码和授权
    async authenticate(password) {
        try {
            // 检查是否被锁定
            const lockoutData = await this.getLockoutData();
            if (lockoutData.isLocked) {
                throw new Error(`账户已锁定，请在 ${Math.ceil((lockoutData.unlockTime - Date.now()) / 60000)} 分钟后重试`);
            }

            // 生成设备指纹
            const deviceFingerprint = await this.cryptoUtils.generateDeviceFingerprint();
            
            // 向服务器验证
            const authResult = await this.verifyWithServer(password, deviceFingerprint);
            
            if (authResult.success) {
                // 清除失败记录
                await this.clearFailureRecord();
                // 存储授权令牌
                await this.storeAuthToken(authResult.token, authResult.expiresAt);
                return { success: true, message: '授权成功' };
            } else {
                // 记录失败尝试
                await this.recordFailureAttempt();
                throw new Error(authResult.message || '授权失败');
            }
        } catch (error) {
            console.error('Authentication error:', error);
            throw error;
        }
    }

    // 向服务器验证
    async verifyWithServer(password, deviceFingerprint) {
        const response = await fetch(`${this.serverUrl}/verify`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                password: await this.hashPassword(password),
                deviceFingerprint: deviceFingerprint,
                timestamp: Date.now()
            })
        });

        if (!response.ok) {
            throw new Error('服务器验证失败');
        }

        return await response.json();
    }

    // 哈希密码
    async hashPassword(password) {
        const encoder = new TextEncoder();
        const data = encoder.encode(password + 'x-enhancer-salt');
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    }

    // 检查当前授权状态
    async checkAuthStatus() {
        const authData = await chrome.storage.local.get(['authToken', 'authExpires']);
        
        if (!authData.authToken || !authData.authExpires) {
            return { isAuthenticated: false, reason: 'no_token' };
        }

        if (Date.now() > authData.authExpires) {
            await chrome.storage.local.remove(['authToken', 'authExpires']);
            return { isAuthenticated: false, reason: 'expired' };
        }

        // 可选：向服务器验证令牌是否仍然有效
        try {
            const isValid = await this.validateTokenWithServer(authData.authToken);
            if (!isValid) {
                await chrome.storage.local.remove(['authToken', 'authExpires']);
                return { isAuthenticated: false, reason: 'invalid_token' };
            }
        } catch (error) {
            console.warn('Token validation failed:', error);
            // 网络错误时允许离线使用
        }

        return { isAuthenticated: true };
    }

    // 向服务器验证令牌
    async validateTokenWithServer(token) {
        try {
            const response = await fetch(`${this.serverUrl}/validate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            });
            return response.ok;
        } catch (error) {
            return false;
        }
    }

    // 存储授权令牌
    async storeAuthToken(token, expiresAt) {
        await chrome.storage.local.set({
            authToken: token,
            authExpires: expiresAt
        });
    }

    // 获取锁定数据
    async getLockoutData() {
        const data = await chrome.storage.local.get(['failureCount', 'lockoutUntil']);
        const failureCount = data.failureCount || 0;
        const lockoutUntil = data.lockoutUntil || 0;
        
        const isLocked = failureCount >= this.maxRetries && Date.now() < lockoutUntil;
        
        return {
            isLocked: isLocked,
            failureCount: failureCount,
            unlockTime: lockoutUntil
        };
    }

    // 记录失败尝试
    async recordFailureAttempt() {
        const data = await chrome.storage.local.get(['failureCount']);
        const failureCount = (data.failureCount || 0) + 1;
        
        const updateData = { failureCount: failureCount };
        
        if (failureCount >= this.maxRetries) {
            updateData.lockoutUntil = Date.now() + this.lockoutTime;
        }
        
        await chrome.storage.local.set(updateData);
    }

    // 清除失败记录
    async clearFailureRecord() {
        await chrome.storage.local.remove(['failureCount', 'lockoutUntil']);
    }

    // 注销
    async logout() {
        await chrome.storage.local.remove(['authToken', 'authExpires']);
    }
}

// 导出到全局
window.CryptoUtils = CryptoUtils;
window.AuthSystem = AuthSystem;
