document.addEventListener('DOMContentLoaded', () => {
  const enabledToggle = document.getElementById('enabledToggle');
  const keyFileInput = document.getElementById('keyFileInput');
  const loadKeyBtn = document.getElementById('loadKeyBtn');
  const clearKeyBtn = document.getElementById('clearKeyBtn');
  const authSection = document.getElementById('authSection');
  const authStatus = document.getElementById('authStatus');
  const statusText = document.getElementById('statusText');
  const keyInputContainer = document.getElementById('keyInputContainer');
  const keyInfo = document.getElementById('keyInfo');
  const message = document.getElementById('message');

  // 加密保护类
  class PopupEncryption {
    calculateChecksum(data) {
      let hash = 0;
      for (let i = 0; i < data.length; i++) {
        const char = data.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
      }
      return hash.toString(16);
    }

    validateKeyFile(keyFileContent) {
      try {
        const keyFile = JSON.parse(keyFileContent);

        if (!keyFile.version || !keyFile.algorithm || !keyFile.key || !keyFile.checksum) {
          throw new Error('\u5bc6\u94a5\u6587\u4ef6\u683c\u5f0f\u65e0\u6548');
        }

        const calculatedChecksum = this.calculateChecksum(keyFile.key);
        if (calculatedChecksum !== keyFile.checksum) {
          throw new Error('\u5bc6\u94a5\u6587\u4ef6\u5df2\u635f\u574f\u6216\u88ab\u7be1\u6539');
        }

        return keyFile;
      } catch (error) {
        throw new Error(`\u5bc6\u94a5\u6587\u4ef6\u9a8c\u8bc1\u5931\u8d25: ${error.message}`);
      }
    }
  }

  const encryption = new PopupEncryption();

  // 显示消息
  function showMessage(text, type = 'error') {
    message.textContent = text;
    message.className = `message ${type}`;
    message.style.display = 'block';

    setTimeout(() => {
      message.style.display = 'none';
    }, 5000);
  }

  // 更新授权状态UI
  function updateAuthUI(isUnlocked, keyFile = null) {
    if (isUnlocked) {
      authSection.classList.add('unlocked');
      authStatus.className = 'status unlocked';
      authStatus.innerHTML = '<span class="status-icon">\ud83d\udd13</span><span>\u5df2\u6388\u6743</span>';
      keyInputContainer.style.display = 'none';
      clearKeyBtn.style.display = 'block';

      if (keyFile) {
        keyInfo.innerHTML = `
          <strong>\u5bc6\u94a5\u4fe1\u606f:</strong><br>
          \u7248\u672c: ${keyFile.version}<br>
          \u7b97\u6cd5: ${keyFile.algorithm}<br>
          \u751f\u6210\u65f6\u95f4: ${new Date(keyFile.timestamp).toLocaleString()}
        `;
        keyInfo.style.display = 'block';
      }
    } else {
      authSection.classList.remove('unlocked');
      authStatus.className = 'status locked';
      authStatus.innerHTML = '<span class="status-icon">\ud83d\udd12</span><span>\u9700\u8981\u5bc6\u94a5\u6587\u4ef6\u6388\u6743</span>';
      keyInputContainer.style.display = 'block';
      clearKeyBtn.style.display = 'none';
      keyInfo.style.display = 'none';
    }
  }

  // 检查当前授权状态
  function checkAuthStatus() {
    chrome.storage.local.get(['keyFileContent', 'isUnlocked'], (data) => {
      if (data.keyFileContent && data.isUnlocked) {
        try {
          const keyFile = encryption.validateKeyFile(data.keyFileContent);
          updateAuthUI(true, keyFile);
        } catch (error) {
          updateAuthUI(false);
          showMessage('\u5bc6\u94a5\u9a8c\u8bc1\u5931\u8d25\uff0c\u8bf7\u91cd\u65b0\u52a0\u8f7d\u5bc6\u94a5\u6587\u4ef6', 'error');
        }
      } else {
        updateAuthUI(false);
      }
    });
  }

  // 加载密钥文件
  loadKeyBtn.addEventListener('click', () => {
    const file = keyFileInput.files[0];
    if (!file) {
      showMessage('\u8bf7\u9009\u62e9\u5bc6\u94a5\u6587\u4ef6', 'error');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const keyFileContent = e.target.result;
        const keyFile = encryption.validateKeyFile(keyFileContent);

        // 保存密钥文件内容
        chrome.storage.local.set({
          keyFileContent: keyFileContent,
          isUnlocked: true
        }, () => {
          updateAuthUI(true, keyFile);
          showMessage('\u5bc6\u94a5\u6587\u4ef6\u52a0\u8f7d\u6210\u529f\uff01', 'success');

          // 通知content script重新加载
          chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
            if (tabs[0] && (tabs[0].url.includes('x.com') || tabs[0].url.includes('twitter.com'))) {
              chrome.tabs.reload(tabs[0].id);
            }
          });
        });

      } catch (error) {
        showMessage(error.message, 'error');
      }
    };

    reader.readAsText(file);
  });

  // 清除密钥
  clearKeyBtn.addEventListener('click', () => {
    if (confirm('\u786e\u5b9a\u8981\u6e05\u9664\u5bc6\u94a5\u6587\u4ef6\u5417\uff1f\u6e05\u9664\u540e\u63d2\u4ef6\u5c06\u65e0\u6cd5\u6b63\u5e38\u5de5\u4f5c\u3002')) {
      chrome.storage.local.remove(['keyFileContent', 'isUnlocked'], () => {
        updateAuthUI(false);
        showMessage('\u5bc6\u94a5\u6587\u4ef6\u5df2\u6e05\u9664', 'success');

        // 通知content script重新加载
        chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
          if (tabs[0] && (tabs[0].url.includes('x.com') || tabs[0].url.includes('twitter.com'))) {
            chrome.tabs.reload(tabs[0].id);
          }
        });
      });
    }
  });

  // Load saved state
  chrome.storage.local.get('isEnabled', (data) => {
    enabledToggle.checked = data.isEnabled === undefined ? true : !!data.isEnabled;
  });

  // Save state on change
  enabledToggle.addEventListener('change', () => {
    chrome.storage.local.set({ isEnabled: enabledToggle.checked });
  });

  // 初始化检查授权状态
  checkAuthStatus();
});
